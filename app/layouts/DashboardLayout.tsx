import { css } from "@emotion/react";
import { ReactNode } from "react";

import { GlobalSidebar } from "~/features/global-sidebar/global-sidebar";

interface DashboardLayoutProps {
  children: ReactNode;
}

const styles = {
  container: css({
    display: "flex",
    height: "100vh",
  }),
  contentContainer: css({
    flex: "1",
    overflow: "auto",
  }),
};

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div css={styles.container}>
      <GlobalSidebar />
      <div css={styles.contentContainer}>{children} </div>
    </div>
  );
}
