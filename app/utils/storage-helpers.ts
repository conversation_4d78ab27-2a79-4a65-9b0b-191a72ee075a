type StorageType = "local" | "session";

const getStorage = (type: StorageType): Storage =>
  type === "local" ? localStorage : sessionStorage;

export const setItem = <T>(key: string, value: T, type: StorageType = "local"): void => {
  try {
    const serialized = JSON.stringify(value);
    getStorage(type).setItem(key, serialized);
  } catch (err) {
    console.error(`Error setting ${key} in ${type}Storage:`, err);
  }
};

export const getItem = <T>(key: string, type: StorageType = "local"): T | null => {
  try {
    const item = getStorage(type).getItem(key);
    return item ? (JSON.parse(item) as T) : null;
  } catch (err) {
    console.error(`Error getting ${key} from ${type}Storage:`, err);
    return null;
  }
};
