import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "../context/reactQueryProvider";
import { ProductService } from "../services/product-list/product-list";

import { useInjection } from "./use-di";
import { OutputPayload } from "~/pages/add-product/add-product";

export type QueryProductListParams = object;

export function useProductList(args: QueryProductListParams) {
  const productService = useInjection<ProductService>(ProductService);

  return useQuery({
    queryKey: [QueryKey.PRODUCT_LIST, args],
    queryFn: () => productService.getProductList(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
