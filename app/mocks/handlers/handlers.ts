import { http, HttpResponse } from "msw";

export const exmapleHandlers = [
  http.get("https://api.example.com/user", () => {
    return HttpResponse.json({
      id: "abc-123",
      firstName: "<PERSON>",
      lastName: "Maverick",
    });
  }),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/ecommerce/products/?limit=20&offset=0&search=&search=&ordering=-marketplace_product__date_submitted&marketplace_product_status=",
    () => {
      return HttpResponse.json({
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            buffer_time_after: "00:10:00",
            buffer_time_before: "00:05:00",
            categories: ["Electronics", "Gadgets"],
            combinations: [],
            created_date: "2025-06-30T12:00:00Z",
            cross_sell_groups: [],
            custom_attributes: [],
            description: "A high-end smart speaker with voice assistant support.",
            duration_range: { min: 15, max: 60 },
            durations: [],
            id: 1,
            images: [
              "https://via.placeholder.com/300x300.png?text=Smart+Speaker",
              "https://via.placeholder.com/300x300.png?text=Speaker+Side+View",
            ],
            interval: "daily",
            is_bookable: true,
            is_large_size: false,
            is_popular: true,
            manage_stock: true,
            marketplace_categories: [
              {
                id: 101,
                title: "Smart Devices",
              },
            ],
            marketplace_product: {
              status: "active",
              status_display: "Available",
              code: 200,
              note: "Live on marketplace",
            },
            max_future_booking: 30,
            minimum_notice: 2,
            minimum_notice_unit: "hours",
            minimum_notice_unit_display: "2 Hours",
            modifier_groups: [],
            operating_hours: [],
            per_item_quantity: 1,
            per_item_type: {
              value: "unit",
              label: "Unit",
            },
            price: "149.99",
            price_range: {
              min: "129.99",
              max: "169.99",
            },
            sale_price: "139.99",
            sale_price_range: {
              min: "119.99",
              max: "149.99",
            },
            share_link: "https://shop.example.com/product/smart-speaker",
            sku: "SMART-SPK-001",
            slug: "smart-speaker",
            status: "active",
            stock_quantity: 45,
            title: "Smart Speaker Pro",
            updated_date: "2025-06-30T14:00:00Z",
            variants: [],
            weight: 1.5,
            weight_unit: {
              value: "kg",
              label: "Kilogram",
            },
            youtube_link: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
          },
        ],
      });
    },
  ),

  http.get(
    "https://************.nip.io/cms/api/translations?filters[namespace][$eqi]=home-acquisition&locale=ar",
    () => {
      return HttpResponse.json({
        app: {
          name: "Roshn Seller Dashboard",
          description: "Manage your seller account and products",
          title: "A home designed for you",
        },
        navigation: {
          home: "Home",
          products: "Products",
          orders: "Orders",
          settings: "Settings",
          profile: "Profile",
          logout: "Logout",
        },
        actions: {
          save: "Save",
          cancel: "Cancel",
          delete: "Delete",
          edit: "Edit",
          create: "Create",
          search: "Search",
          filter: "Filter",
          clear: "Clear",
          apply: "Apply",
        },
        messages: {
          loading: "Loading...",
          error: "An error occurred",
          success: "Operation successful",
          confirmDelete: "Are you sure you want to delete this item?",
        },
      });
    },
  ),
];
