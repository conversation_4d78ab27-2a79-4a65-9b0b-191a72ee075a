import { inject, injectable } from "inversify";

import { ReactQueryClient } from "~/services/react-query-client";
import { RestHelper } from "~/services/rest-helper";

import { GetProductListResponse } from "./product-list";

@injectable()
export class ProductListImpl {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
    @inject(ReactQueryClient)
    private readonly reactQueryClient: ReactQueryClient,
  ) {}

  getProductList: (args: {
    marketplace_product_status: string;
  }) => Promise<GetProductListResponse> = (args: { marketplace_product_status: string }) => {
    return this.restHelper.get("/api/v1.2/ecommerce/products/", {
      params: {
        limit: 20,
        offset: 0,
        search: "",
        ordering: "-marketplace_product__date_submitted",
        ...args,
      },
    });
  };

  createProduct: (args: any) => Promise<GetProductListResponse> = (args: any) => {
    return this.restHelper.post("/api/v1.2/ecommerce/products/", {
      data: args
    });
  };
}
