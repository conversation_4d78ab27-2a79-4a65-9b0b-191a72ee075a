import { css } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";

import RoshnLogo from "~/assets/logos/roshn-logo.svg?url";

const styles = {
  authWrapper: (theme: AppTheme) =>
    css({
      padding: theme.rds.dimension["400"],
      backgroundColor: theme.rds.color.background.ui.primary.default,
      minWidth: theme.rds.dimension["4000"],
      width: "640px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: theme.rds.dimension["300"],
      textAlign: "center",
    }),

  logo: css({
    width: "130px",
    height: "40px",
    marginBlock: "42px",
  }),
};

export function BrandWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div css={styles.authWrapper}>
      <img src={RoshnLogo} alt="Roshn Logo" css={styles.logo} />
      {children}
    </div>
  );
}
