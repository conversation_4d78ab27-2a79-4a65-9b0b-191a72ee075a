import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AppTheme,
  createBoxShadowString,
  RDSButton,
  RDSEmptyState,
  RDSModal,
  RDSProgressSteps,
  RDSTypography,
} from "@roshn/ui-kit";
import { Fragment, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import RoshnLogo from "~/assets/logos/roshn-white-logo.svg?url";
import { Input } from "~/components/form-components/input/input";
import { TextArea } from "~/components/form-components/text-area/text-area";
import { useStepValidator } from "~/hooks/use-step-validator";

type StepState = "active" | "complete" | "in-complete";

type StepId = "personal-info" | "company-info" | "complete";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const styles = {
  wrapper: {
    display: "flex",
    width: "100vw",
    height: "100vh",
  },

  logo: (theme: AppTheme) =>
    css({
      position: "absolute",
      top: theme.rds.dimension["600"],
      left: theme.rds.dimension["1000"],
      width: "250px",
      height: "80px",
    }),

  contentWrapper: (theme: AppTheme) =>
    css({
      marginBlockStart: "150px",
      width: "100%",
      height: "calc(100% - 150px)",
      backgroundColor: theme.rds.color.background.ui.canvas,
      paddingInline: theme.rds.dimension["2000"],
      paddingBlock: theme.rds.dimension["500"],
      borderRadius: "24px 24px 0 0",
      zIndex: 0,
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["500"],
      textAlign: "center",
      alignItems: "center",
    }),

  heading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h3,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  bottomBar: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.xl,
      width: "100%",
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: theme.rds.color.background.ui.primary.default,
      boxShadow: createBoxShadowString(theme.rds.elevation.onTop["100"]),
      display: "flex",
      justifyContent: "space-between",
      paddingInline: "80px",
      paddingBlock: "24px",
      alignItems: "center",
      color: theme.rds.color.text.ui.primary,
      textTransform: "none",
    }),

  bottomBarButton: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
    }),

  button: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["200"],
    }),

  fieldsWrapper: css({
    width: "100%",
    display: "grid",
    gridTemplateColumns: "repeat(2, 1fr)",
    gap: "24px",
  }),

  emptyStateWrapper: css({
    height: "fit-content",
  }),

  emptyStateBottomBar: css({
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  }),

  emptyStateButton: css({
    marginBlockStart: "8px",
    textTransform: "none",
  }),
};

export const schema = z.object({
  firstName: z.string().min(1, { message: "First name is required" }).trim(),
  jobTitle: z.string().min(1, { message: "Job title is required" }).trim(),
  email: z.string().email({ message: "Invalid email address" }).trim().toLowerCase(),
  phoneNumber: z.string().regex(/^[0-9]{10}$/, { message: "Enter a valid 10-digit phone number" }),

  businessType: z.string().min(1, { message: "Business type is required" }).trim(),
  companyName: z.string().min(1, { message: "Company name is required" }).trim(),
  companyRegNumber: z.string().min(1, { message: "CRN is required" }).trim(),
  portfolioUrl: z.string().url({ message: "Invalid URL" }).optional().or(z.literal("")),
  comments: z.string().optional().or(z.literal("")),
});

export default function Onboard() {
  const [showCancelModal, setShowCancelModal] = useState(false);

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Personal info", state: "active", id: "personal-info" },
    { stepTitle: "Company info", state: "in-complete", id: "company-info" },
    { stepTitle: "Complete!", state: "in-complete", id: "complete" },
  ]);

  const [currentStep, setCurrentStep] = useState<StepId>("personal-info");

  const { control } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      businessType: "",
      comments: "",
      companyName: "",
      companyRegNumber: "",
      email: "",
      firstName: "",
      jobTitle: "",
      phoneNumber: "",
      portfolioUrl: "",
    },
  });

  const handleNextStep = () => {
    const currentIndex = steps.findIndex((step) => step.id === currentStep);
    if (currentIndex < steps.length - 1) {
      const nextStepId = steps[currentIndex + 1].id;
      handleStepChange(nextStepId);
    }
  };

  const handleStepChange = (stepId: StepId) => {
    setCurrentStep(stepId);
    setSteps((prevSteps) =>
      prevSteps.map((step) => {
        if (step.id === stepId) {
          return { ...step, state: "active" };
        } else if (step.id === "complete") {
          return { ...step, state: "in-complete" };
        }
        return { ...step, state: "complete" };
      }),
    );
  };

  const personalInfo = (
    <Fragment key="personalInfo">
      <RDSTypography css={styles.heading}>Hello! Let’s get you started with ROSHN</RDSTypography>
      <div css={styles.fieldsWrapper}>
        <Input
          data-testid="first-name"
          control={control}
          name="firstName"
          label="First Name"
          isRequired
          placeholder="Faisal Al-Otaibi..."
          helperText="Provide your full legal name."
        />
        <Input
          data-testid="job-title"
          control={control}
          name="jobTitle"
          label="Job Title"
          isRequired
          placeholder="Business Development Manager..."
          helperText="Your role or position at the company."
        />
        <Input
          data-testid="work-email"
          control={control}
          name="email"
          label="Work email"
          isRequired
          placeholder="<EMAIL>..."
          helperText="Use your company email address to speed up verification."
        />
        <Input
          data-testid="phone-number"
          control={control}
          name="phoneNumber"
          label="Phone Number"
          isRequired
          placeholder="+966 55 123 4567..."
          helperText="Include your country code."
        />
      </div>
    </Fragment>
  );

  const companyInfo = (
    <Fragment key="companyInfo">
      <RDSTypography css={styles.heading}>Provide your company’s information</RDSTypography>
      <div css={styles.fieldsWrapper}>
        <Input
          control={control}
          name="businessType"
          label="Business type"
          data-testid="business-type"
          isRequired
          placeholder="Select business type..."
          helperText="Choose the category that best describes your company."
        />
        <Input
          control={control}
          name="companyName"
          label="Company name"
          data-testid="company-name"
          isRequired
          placeholder="Example inc..."
          helperText="Provide your company’s legal name."
        />
        <Input
          control={control}
          name="companyRegNumber"
          label="Company Registration Number (CRN)"
          data-testid="company-reg-number"
          isRequired
          placeholder="1122334455..."
          helperText="Find this on your official business registration documents."
        />
        <Input
          control={control}
          name="portfolioUrl"
          data-testid="portfolio-url"
          label="Portfolio or website URL (optional)"
          placeholder="https://example.com..."
          helperText="Provide your company's main portfolio or official website."
        />
      </div>
      <TextArea
        control={control}
        name="comments"
        placeholder="Share any other relevant details here..."
        label="Comments (optional)"
        helperText="Add comments or additional context to support your enrollment."
      />
    </Fragment>
  );

  const complete = (
    <div css={styles.emptyStateWrapper} key="complete">
      <RDSEmptyState
        appearance="success"
        size="sm"
        description="Our team will review your information within 2 business days.
        We’ll notify you by email once your account is activated."
        title="Enrollment submitted successfully!"
        buttons={[
          {
            text: "Learn more about ROSHN Sellers",
            variant: "primary",
            css: { textTransform: "none" },
          },
        ]}
      />
      <div css={styles.emptyStateBottomBar}>
        <RDSTypography>Need help? Our support team is here for you anytime.</RDSTypography>
        <RDSButton
          css={styles.emptyStateButton}
          variant="tertiary"
          size="lg"
          text="Contact Support"
        />
      </div>
    </div>
  );

  const stepRenderer: Record<StepId, JSX.Element> = {
    "personal-info": personalInfo,
    "company-info": companyInfo,
    complete,
  };

  const stepFieldsMap = {
    "personal-info": ["firstName", "jobTitle", "email", "phoneNumber"],
    "company-info": ["businessType", "companyName", "companyRegNumber", "portfolioUrl", "comments"],
    complete: [],
  };

  const isValid = useStepValidator(currentStep, control, schema, stepFieldsMap);

  return (
    <>
      <RDSModal
        headerProps={{
          label: "Exit enrollment?",
          type: "centred",
        }}
        isOpen={showCancelModal}
        buttonsGroup={{
          buttons: [
            <RDSButton variant="primary" text="Discard enrollment" key="enrollment" />,
            <RDSButton
              variant="secondary"
              onClick={() => setShowCancelModal(false)}
              text="No, continue working"
              key="continue"
            />,
          ],
          direction: "vertical",
        }}
        description={`You haven’t completed the enrollment form yet. Exiting now will discard all the \ninformation you’ve entered. Are you sure you want to leave?`}
        showContent
        showDescription
      />
      <div css={styles.wrapper}>
        <img src={RoshnLogo} alt="Roshn Logo" css={styles.logo} />

        <div css={styles.contentWrapper}>
          <RDSProgressSteps type="number" steps={steps} size="md" />
          {stepRenderer[currentStep]}
        </div>
        {currentStep !== "complete" && (
          <div css={styles.bottomBar}>
            <RDSTypography>Complete your details to activate your seller account</RDSTypography>
            <div css={styles.bottomBarButton}>
              <RDSButton
                css={styles.button}
                variant="secondary"
                onClick={() => setShowCancelModal(true)}
                size="lg"
                text="Cancel"
              />
              <RDSButton
                onClick={handleNextStep}
                css={styles.button}
                variant="primary"
                size="lg"
                disabled={!isValid}
                text="Continue"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
