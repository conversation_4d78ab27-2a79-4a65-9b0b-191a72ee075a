import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "@remix-run/react";
import { AppTheme, RDS<PERSON>utton, RDSTypography } from "@roshn/ui-kit";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { BrandWrapper } from "~/components/brand-wrapper/brand-wrapper";
import { Input } from "~/components/form-components/input/input";

const styles = {
  wrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100vw",
    height: "100vh",
  }),

  headingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.primary.secondary,
      marginBlockEnd: theme.rds.dimension["200"],
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      width: "100%",
    }),
};

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Must include an uppercase letter")
      .regex(/[a-z]/, "Must include a lowercase letter")
      .regex(/[0-9]/, "Must include a number"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function ActivateAccount() {
  const { emailAddress } = useParams();

  const {
    control,
    formState: { isValid },
  } = useForm({
    resolver: zodResolver(resetPasswordSchema),
    mode: "onChange",
    defaultValues: { confirmPassword: "", password: "" },
  });

  return (
    <div css={styles.wrapper}>
      <BrandWrapper>
        <div css={styles.sectionWrapper}>
          <RDSTypography css={styles.headingText}>Activate your account</RDSTypography>
          <RDSTypography
            css={styles.descriptionText}
          >{`You are setting a new password for ${emailAddress ?? ""}.`}</RDSTypography>
        </div>
        <div css={styles.sectionWrapper}>
          <Input
            control={control}
            data-testid="password"
            name="password"
            label="New Password"
            placeholder="Enter your new password"
            type="password"
            helperText="Must be at least 8 characters and include letters and numbers."
          />
          <Input
            control={control}
            data-testid="confirm-pass"
            name="confirmPassword"
            label="Confirm Password"
            placeholder="Re-enter your new password"
            type="password"
            helperText="Must be at least 8 characters with letters and numbers."
          />
        </div>
        <div css={styles.sectionWrapper}>
          <RDSButton
            css={{ textTransform: "none" }}
            variant="primary"
            data-testid="login-btn"
            size="lg"
            text="Set password and log in"
            onClick={() => {}}
            disabled={!isValid}
          />
        </div>
      </BrandWrapper>
    </div>
  );
}
