import { css, useTheme } from "@emotion/react";
import {
  RDSTagInteractive,
  RDSDashboardTab,
  RDSDashboardInsight,
  RDSTypography,
  AppTheme,
} from "@roshn/ui-kit";

import RoshnLogo from "~/assets/logos/roshn-logo.svg?url";

const styles = {
  container: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension[200],
      padding: `${theme?.rds.dimension["400"]} ${theme?.rds.dimension["600"]}`,
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
      minHeight: "100vh",
      width: "fit-content",
    }),
  header: (theme: AppTheme) =>
    css({
      marginBottom: theme?.rds.dimension["400"],
    }),
  metricsSection: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension["300"],
    }),
  tagsRow: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme?.rds.dimension["200"],
    }),
  metricsGrid: (theme: AppTheme) =>
    css({
      display: "grid",
      gridTemplateColumns: "repeat(4, 1fr)",
      gap: theme?.rds.dimension["200"],
      marginBottom: theme?.rds.dimension["400"],
    }),
  insightsSection: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension["300"],
    }),
  insightsList: css({
    display: "flex",
    flexDirection: "column",
  }),
};

const timeFilters = [
  { label: "This week", state: "active" },
  { label: "This month", state: "default" },
  { label: "Last 6 months", state: "default" },
  { label: "This year", state: "default" },
  { label: "All time", state: "default" },
];

const metrics = [
  {
    title: "Total Sales",
    currency: "SAR",
    amount: "999.000.000",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "2%",
    percentageChangeType: "increase",
  },
  {
    title: "Total Sales",
    currency: "SAR",
    amount: "500",
    percentageChange: "-5%",
    percentageChangeType: "decrease",
  },
  {
    title: "Total Sales",
    currency: "SAR",
    amount: "999.000.000",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "2%",
    percentageChangeType: "increase",
  },
];

const insights = [
  {
    title: "Increasing Demand in Downtown",
    description: "Property inquiries in Downtown Riyadh have increased by 12% in the last 30 days.",
    date: "June 28, 2024",
    tag: "new",
    buttonText: "View properties",
    tagRequired: true,
    icon: RoshnLogo,
  },
  {
    title: "Price Adjustment Recommended",
    description:
      "Your villa in Palm Jumeirah is priced 8% above similar properties, consider adjusting.",
    date: "June 27, 2024",
    tag: "new",
    buttonText: "Review pricing",
    tagRequired: true,
    icon: RoshnLogo,
  },
  {
    title: "Expiring Listings",
    description: "3 of your listings are expiring in the next 7 days.",
    date: "June 25, 2024",
    buttonText: "Renew listings",
    tagRequired: false,
    icon: RoshnLogo,
  },
  {
    title: "Customer Behavior",
    description:
      "Most inquiries come in between 6-8 PM. Consider scheduling follow-ups during this time.",
    date: "June 23, 2024",
    buttonText: "See analytics",
    tagRequired: false,
    icon: RoshnLogo,
  },
  {
    title: "Boost Conversion Rate",
    description: "Property inquiries in Downtown Riyadh have increased by 12% in the last 30 days.",
    date: "June 28, 2024",
    buttonText: "Get started",
    tagRequired: false,
    icon: RoshnLogo,
  },
];

export default function LandingPage() {
  const theme = useTheme() as AppTheme;
  return (
    <div css={styles.container(theme)}>
      <RDSTypography fontName={theme?.rds?.typographies.display.d5} css={styles.header(theme)}>
        Merchant Dashboard
      </RDSTypography>
      <section css={styles.metricsSection(theme)}>
        <RDSTypography fontName={theme?.rds?.typographies.heading.h4}>
          Dashboard metrics
        </RDSTypography>
        <div css={styles.tagsRow(theme)}>
          {timeFilters.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              label={tag.label}
              state={tag.state as "default" | "active"}
              // Add onClick handler as needed
            />
          ))}
        </div>
        <div css={styles.metricsGrid(theme)}>
          {metrics.map((metric) => (
            <RDSDashboardTab
              key={metric.title}
              amount={metric.amount}
              title={metric.title}
              currency={metric.currency}
              tooltipContent={metric?.tooltipContent}
              percentageChange={metric.percentageChange}
              percentageChangeType={metric.percentageChangeType}
            />
          ))}
        </div>
      </section>
      <section css={styles.insightsSection(theme)}>
        <RDSTypography fontName={theme?.rds?.typographies.heading.h4}>Smart insights</RDSTypography>
        <div css={styles.insightsList}>
          {insights.map((insight) => (
            <RDSDashboardInsight
              key={insight.title}
              icon={insight.icon}
              title={insight.title}
              description={insight.description}
              date={insight.date}
              buttonText={insight.buttonText}
              tagRequired={insight.tagRequired}
              insightClickHandler={(e) => console.log(e.target)}
              // Add onAction handler as needed
            />
          ))}
        </div>
      </section>
    </div>
  );
}
