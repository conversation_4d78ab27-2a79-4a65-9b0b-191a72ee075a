import { css, useTheme } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
} from "@roshn/ui-kit";
import { useState } from "react";

import { RoshnLoadingModal } from "~/features/common/loading/roshn-loading-modal";
import { useAppPath } from "~/hooks/use-app-path";
import { QueryProductListParams, useProductList } from "~/hooks/use-product-list";
import { Product } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";

const tagData = [
  { label: "All", state: "active" },
  { label: "Approved", state: "default" },
];

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      padding: theme.rds.dimension["600"],
      background: theme.rds.color.background.ui.canvas,
      minHeight: "100vh",
    }),
  header: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.rds.dimension["600"],
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),
};

export default function ProductsPage() {
  const theme = useTheme() as AppTheme;
  const [activeTag, setActiveTag] = useState("All");
  const generatePath = useAppPath();
  const navigate = useNavigate();

  const productListParams: QueryProductListParams = {
    marketplace_product_status: activeTag === "All" ? "" : "APPROVED",
  };

  const { data, isFetching } = useProductList(productListParams);
  const productList = data?.results || [];

  const tableColumnData = productList.map((product: Product, index: number) => ({
    id: `row-${index}`,
    column1: product?.title,
    column2: product.status,
    column3: product?.marketplace_categories[0]?.title,
    column4: `${product?.stock_quantity} in stock`,
    column5: product?.price,
  }));

  const tableData = {
    title: "Products List",
    columns: [
      {
        id: "column1",
        header: "Product",
        accessor: "column1",
        type: "text",
      },
      {
        id: "column2",
        header: "Status",
        accessor: "column2",
        type: "tag",
      },
      {
        id: "column3",
        header: "Category",
        accessor: "column3",
        type: "text",
      },
      {
        id: "column4",
        header: "Inventory",
        accessor: "column4",
        type: "lead",
      },
      {
        id: "price",
        header: "Price",
        accessor: "id",
        type: "action",
      },
    ],
    data: tableColumnData,
  };

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.header(theme)}>
        <div>
          <RDSTypography fontName={theme?.rds?.typographies?.display.d5}>Products</RDSTypography>
        </div>
        <div>
          <RDSButton size="lg" text="+ Add product" onClick={() => navigate(generatePath(AppPaths.addProduct))} />
        </div>
      </div>
      <div style={{ marginBottom: theme.rds.dimension["400"] }}>
        <RDSSearchInput
          type="text"
          placeholder="Search by name, city, status..."
          css={styles.searchInput(theme)}
        />
        <div css={styles.tagContainer(theme)}>
          {tagData.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              size="md"
              label={tag.label}
              state={tag.label === activeTag ? "active" : "default"}
              onClick={() => setActiveTag(tag.label)}
            />
          ))}
        </div>
      </div>
      {isFetching ? (
        <RoshnLoadingModal />
      ) : (
        <RDSTable
          title={tableData.title}
          description={tableData.description}
          columns={tableData.columns}
          data={tableData.data}
          pagination={false}
        />
      )}
    </div>
  );
}
