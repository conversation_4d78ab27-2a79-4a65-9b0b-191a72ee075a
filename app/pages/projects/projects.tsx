import { css, useTheme } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
} from "@roshn/ui-kit";

import { useAppPath } from "~/hooks/use-app-path";
import { useProductList } from "~/hooks/use-product-list";
import { AppPaths } from "~/utils/app-paths";

const tagData = [
  { label: "All", state: "active" },
  { label: "Drafted", state: "default" },
  { label: "In review", state: "default" },
  { label: "Published", state: "default" },
];

const mockData = Array(10)
  .fill(null)
  .map((_, index) => ({
    id: `row-${index}`,
    column1: `Some type of content ${index + 1}`,
    column2: `Some type of content ${index + 1}`,
    column3: `Tag ${index + 1}`,
    column4: `Lead content ${index + 1}`,
    column5: `Some type of content ${index + 1}`,
  }));

const tableData = {
  title: "Table title (amount of records)",
  description: "Description, call to action or amount of records",
  columns: [
    {
      id: "checkbox",
      header: "",
      accessor: "id",
      type: "checkbox",
    },
    {
      id: "column1",
      header: "Text column",
      accessor: "column1",
      type: "text",
    },
    {
      id: "column2",
      header: "Text column",
      accessor: "column2",
      type: "text",
    },
    {
      id: "column3",
      header: "Tag column",
      accessor: "column3",
      type: "tag",
    },
    {
      id: "column4",
      header: "Lead column",
      accessor: "column4",
      type: "lead",
    },
    {
      id: "actions",
      header: "Actions",
      accessor: "id",
      type: "action",
    },
  ],
  data: mockData,
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      padding: theme.rds.dimension["600"],
      background: theme.rds.color.background.ui.canvas,
      minHeight: "100vh",
    }),
  header: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      alignContent: "center",
      marginBottom: theme.rds.dimension["600"],
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),
};

export default function ProjectsPage() {
  const theme = useTheme() as AppTheme;
  const productList = useProductList({});
  const generatePath = useAppPath();
  const navigate = useNavigate();

  const handleAddProject = () => {
    navigate(generatePath(AppPaths.addProject));
  };

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.header(theme)}>
        <div>
          <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>Projects</RDSTypography>
        </div>
        <div>
          <RDSButton onClick={handleAddProject} size="lg" text="+ Add project" />
        </div>
      </div>
      <div style={{ marginBottom: theme.rds.dimension["400"] }}>
        <RDSSearchInput
          type="text"
          placeholder="Search by name, city, status..."
          css={styles.searchInput(theme)}
        />
        <div css={styles.tagContainer(theme)}>
          {tagData.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              label={tag.label}
              state={tag.state as "default" | "active"}
            />
          ))}
        </div>
      </div>
      <RDSTable
        title={tableData.title}
        description={tableData.description}
        columns={tableData.columns}
        data={tableData.data}
        pagination={false}
      />
    </div>
  );
}
