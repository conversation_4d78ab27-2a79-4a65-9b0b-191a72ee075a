import { useLocation, useNavigate } from "@remix-run/react";
import { RDSDashboardSidebar } from "@roshn/ui-kit";
import { useCallback } from "react";

import { createSvg } from "~/components/svgs";
import { labelToAppPathKey } from "~/constants/global-slider-routes";
import { useAppPath } from "~/hooks/use-app-path";
import { useSideBarStore } from "~/store/useSideBarStore";
import { AppPaths } from "~/utils/app-paths";

import { SidebarState } from "./sidebar-slice";

const sideBarLabels = {
  notification: "Notifications",
  profile: "My Account",
  navigate: "Navigate",
};

const AppointmentsIcon = createSvg(() => import("~/assets/icons/Appointments/Appointments.svg"));
const BuildingIcon = createSvg(() => import("~/assets/icons/Building/Building.svg"));
const BusinessIcon = createSvg(() => import("~/assets/icons/Business/Business.svg"));
const DashboardIcon = createSvg(() => import("~/assets/icons/Dashboard/Dashboard.svg"));
const InquiriesIcon = createSvg(() => import("~/assets/icons/Inqueries/Inqueries.svg"));

export function GlobalSidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { sidebarStatus, openSidebar, closeSidebar } = useSideBarStore();

  const generatePath = useAppPath();

  const handleClose = useCallback(() => {
    closeSidebar();
  }, []);

  const handleOpen = useCallback(() => {
    openSidebar();
  }, []);

  const handleProfileClick = useCallback(() => {
    navigate(generatePath(AppPaths.profile));
  }, [navigate]);

  const navigationLinks: { label: string; icon: React.ReactNode; active: boolean }[] = [
    {
      label: "Dashboard",
      icon: <DashboardIcon />,
      active: location.pathname === generatePath(AppPaths.dashboard),
    },
    {
      label: "Projects",
      icon: <BuildingIcon />,
      active: location.pathname === generatePath(AppPaths.projects),
    },
    {
      label: "Products",
      icon: <BuildingIcon />,
      active: location.pathname === generatePath(AppPaths.products),
    },
    {
      label: "Inquiries",
      icon: <InquiriesIcon />,
      active: location.pathname === generatePath(AppPaths.inquiries),
    },
    {
      label: "Appointments",
      icon: <AppointmentsIcon />,
      active: location.pathname === generatePath(AppPaths.appointments),
    },
    {
      label: "Whatsapp Business",
      icon: <BusinessIcon />,
      active: location.pathname === generatePath(AppPaths.business),
    },
  ];

  const handleNavigationClick = useCallback(
    ({ label }: { label: string }) => {
      const key = labelToAppPathKey[label.toLocaleLowerCase()];
      if (key) {
        navigate(generatePath(AppPaths[key]));
      }
    },
    [navigate],
  );

  return (
    <RDSDashboardSidebar
      isOpen={sidebarStatus === SidebarState.OPEN}
      onClose={handleClose}
      onOpen={handleOpen}
      roshnLogo="https://alb-home.roshn.sa/roshn_Group_Logo_2ce51d7009/roshn_Group_Logo_2ce51d7009.svg"
      navigationLinks={navigationLinks}
      sideBarLabels={sideBarLabels}
      onNavigationClick={(e) => handleNavigationClick(e)}
      onProfileClick={handleProfileClick}
    />
  );
}
